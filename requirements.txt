# Build tools first
setuptools>=60.0.0
wheel>=0.37.0

# Core dependencies - flexible versions for better compatibility
python-telegram-bot>=20.0
instagrapi==1.19.8
Pillow>=9.0.0
requests>=2.25.0
python-dotenv>=0.19.0
schedule>=1.1.0
pytz>=2023.3

# Video and image processing - use precompiled wheels when possible
opencv-python>=4.5.0
moviepy>=1.0.0
imageio>=2.20.0
imageio-ffmpeg>=0.4.0
numpy>=1.21.0

# Text processing and fonts
arabic-reshaper>=3.0.0
python-bidi>=0.4.0
emoji>=2.0.0

# Logging and utilities
colorlog>=6.0.0
tqdm>=4.60.0
psutil>=5.8.0

# Testing (optional)
pytest>=7.0.0
pytest-asyncio>=0.20.0
